import { randomUUID } from 'node:crypto';
import { ServiceRequest } from '@skillspace/grpc';

import { app, pageService } from './test-setup';

describe('PageService Bulk Operations Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(pageService).toBeDefined();
    });

    describe('createManyPages', () => {
        it('должен создать несколько страниц одновременно', async () => {
            const request: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'bulk-service-1',
                        entityId: 'bulk-entity-1',
                        schoolId: 'bulk-school-1',
                    },
                    {
                        serviceId: 'bulk-service-2',
                        entityId: 'bulk-entity-2',
                        schoolId: 'bulk-school-2',
                    },
                    {
                        serviceId: 'bulk-service-3',
                        entityId: 'bulk-entity-3',
                        schoolId: 'bulk-school-3',
                    },
                ],
            };

            const response = await pageService.createManyPages(request);

            expect(response).toBeDefined();
            expect(response.pages).toHaveLength(3);

            // Проверяем первую страницу
            expect(response.pages[0]).toBeDefined();
            expect(response.pages[0].pageId).toBeDefined();
            expect(response.pages[0].serviceId).toBe('bulk-service-1');
            expect(response.pages[0].entityId).toBe('bulk-entity-1');
            expect(response.pages[0].schoolId).toBe('bulk-school-1');

            // Проверяем вторую страницу
            expect(response.pages[1]).toBeDefined();
            expect(response.pages[1].pageId).toBeDefined();
            expect(response.pages[1].serviceId).toBe('bulk-service-2');
            expect(response.pages[1].entityId).toBe('bulk-entity-2');
            expect(response.pages[1].schoolId).toBe('bulk-school-2');

            // Проверяем третью страницу
            expect(response.pages[2]).toBeDefined();
            expect(response.pages[2].pageId).toBeDefined();
            expect(response.pages[2].serviceId).toBe('bulk-service-3');
            expect(response.pages[2].entityId).toBe('bulk-entity-3');
            expect(response.pages[2].schoolId).toBe('bulk-school-3');

            // Проверяем, что страницы действительно созданы в БД
            for (const pageResponse of response.pages) {
                const foundPage = await pageService.findById(pageResponse.pageId);
                expect(foundPage).toBeDefined();
                expect(foundPage.id).toBe(pageResponse.pageId);
                expect(foundPage.serviceId).toBe(pageResponse.serviceId);
                expect(foundPage.entityId).toBe(pageResponse.entityId);
                expect(foundPage.schoolId).toBe(pageResponse.schoolId);
            }
        });

        it('должен создать пустой ответ при передаче пустого массива', async () => {
            const request: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [],
            };

            const response = await pageService.createManyPages(request);

            expect(response).toBeDefined();
            expect(response.pages).toHaveLength(0);
        });

        it('должен создать одну страницу', async () => {
            const request: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'single-service',
                        entityId: 'single-entity',
                        schoolId: 'single-school',
                    },
                ],
            };

            const response = await pageService.createManyPages(request);

            expect(response).toBeDefined();
            expect(response.pages).toHaveLength(1);
            expect(response.pages[0].serviceId).toBe('single-service');
            expect(response.pages[0].entityId).toBe('single-entity');
            expect(response.pages[0].schoolId).toBe('single-school');
        });
    });

    describe('removeManyPages', () => {
        it('должен удалить несколько существующих страниц', async () => {
            // Создаем тестовые страницы
            const createRequest: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'remove-service-1',
                        entityId: 'remove-entity-1',
                        schoolId: 'remove-school-1',
                    },
                    {
                        serviceId: 'remove-service-2',
                        entityId: 'remove-entity-2',
                        schoolId: 'remove-school-2',
                    },
                ],
            };

            const createResponse = await pageService.createManyPages(createRequest);
            expect(createResponse.pages).toHaveLength(2);

            // Проверяем, что страницы существуют
            for (const page of createResponse.pages) {
                const foundPage = await pageService.findById(page.pageId);
                expect(foundPage).toBeDefined();
            }

            // Удаляем страницы
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: createResponse.pages.map((page) => page.pageId),
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(2);
            expect(removeResponse.removedPageIds).toContain(createResponse.pages[0].pageId);
            expect(removeResponse.removedPageIds).toContain(createResponse.pages[1].pageId);

            // Проверяем, что страницы удалены
            for (const page of createResponse.pages) {
                await expect(pageService.findById(page.pageId)).rejects.toThrow('not found');
            }
        });

        it('должен корректно обработать удаление несуществующих страниц', async () => {
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: [randomUUID(), randomUUID()],
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(0); // Ни одна страница не была удалена
        });

        it('должен корректно обработать смешанный случай (существующие и несуществующие страницы)', async () => {
            // Создаем одну тестовую страницу
            const createRequest: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'mixed-service',
                        entityId: 'mixed-entity',
                        schoolId: 'mixed-school',
                    },
                ],
            };

            const createResponse = await pageService.createManyPages(createRequest);
            expect(createResponse.pages).toHaveLength(1);

            // Пытаемся удалить существующую и несуществующую страницы
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: [
                    createResponse.pages[0].pageId, // существующая
                    randomUUID(), // несуществующая
                ],
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(1); // только существующая удалена
            expect(removeResponse.removedPageIds).toContain(createResponse.pages[0].pageId);

            // Проверяем, что существующая страница удалена
            await expect(pageService.findById(createResponse.pages[0].pageId)).rejects.toThrow('not found');
        });

        it('должен корректно обработать пустой массив', async () => {
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: [],
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(0);
        });
    });
});
