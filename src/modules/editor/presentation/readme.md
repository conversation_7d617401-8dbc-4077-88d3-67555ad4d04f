### 2. Серве<PERSON> (Consumer)

```ts

@Module({
    imports: [
        GrpcModule.register({
            packageName: 'monolith',
            address: '0.0.0.0:50051',
        }),
    ],
})
export class AppModule {
}
```

```typescript
@GrpcSubscribe('MonolithService', 'GetUserPermission', 'v1')
handleGetUserPermission(data: ServiceRequest<'MonolithService', 'GetUser', 'v1'>;
): Promise < ServiceResponse < 'MonolithService', 'GetUser', 'v1' >> {
    return { allowed: true },;
};
```

---

### 3. Клиент (Producer)

```ts

@Module({
    imports: [
        GrpcClientModule.register({
            serviceName: 'MonolithService',
            address: '0.0.0.0:50051',
        }),
    ],
})
export class MyFeatureModule {
}
```

```ts

@Injectable()
export class MyService {
    constructor(
        @GrpcInjectClient('MonolithService') private readonly grpc: GrpcClient<'MonolithService'>,
    ) {
    }

    async doSomething() {
        const res = await this.grpc.send('GetUserPermission', 'v1', { userId: '123' });
    }
}
```
