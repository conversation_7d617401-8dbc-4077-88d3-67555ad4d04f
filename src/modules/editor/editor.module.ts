import { Module } from '@nestjs/common';

import { PageService } from './application/services/page.service';
import { PageRepo } from './infrastructure/repositories/page.repo';
import { PAGE_REPO } from './injects';
import { PageResolver } from './presentation/graphql/page.resolver';
import { PageManagement } from './presentation/page-management.consumer';

@Module({
    providers: [{ provide: PAGE_REPO, useClass: PageRepo }, PageService, PageResolver, PageManagement],
    exports: [PageService, PAGE_REPO],
})
export class EditorModule {}
